@extends('admin.layouts.app')

@section('title', 'داشبورد')

@section('breadcrumb')
    <li class="breadcrumb-item active">داشبورد</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title h3">داشبورد مدیریت</h1>
            <p class="text-muted">خلاصه وضعیت سیستم و آمار کلی</p>
        </div>
    </div>

    <!-- کارت‌های آماری -->
    <div class="row mb-4">
        <!-- تعداد کاربران -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card primary">
                        <i class="fas fa-users stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($countUsers) }}</div>
                            <div class="stat-label">تعداد کاربران</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تراکنش‌های امروز -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card success">
                        <i class="fas fa-dollar-sign stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($data['today_transactions']['value'] ?? 0) }}</div>
                            <div class="stat-label">مجموع واریز تومانی امروز</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تعداد تراکنش‌های موفق -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card info">
                        <i class="fas fa-check-circle stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($data['successful_transactions'] ?? 0) }}</div>
                            <div class="stat-label">تراکنش‌های موفق امروز</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- درآمد ماهانه -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card warning">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($data['monthly_revenue'] ?? 0) }}</div>
                            <div class="stat-label">درآمد ماهانه (تومان)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- کارت‌های آماری تکمیلی -->
    <div class="row mb-4">
        <!-- کاربران جدید امروز -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary-subtle rounded-3 p-3">
                                <i class="fas fa-user-plus text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">کاربران جدید</h6>
                            <h4 class="mb-0 text-primary">{{ $data['new_users_today'] ?? 0 }}</h4>
                            <small class="text-muted">امروز</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مجموع موجودی -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success-subtle rounded-3 p-3">
                                <i class="fas fa-wallet text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">مجموع موجودی</h6>
                            <h4 class="mb-0 text-success">{{ number_format($data['total_balance'] ?? 0) }}</h4>
                            <small class="text-muted">تومان</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تراکنش‌های در انتظار -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning-subtle rounded-3 p-3">
                                <i class="fas fa-clock text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">در انتظار تایید</h6>
                            <h4 class="mb-0 text-warning">{{ $data['pending_transactions'] ?? 0 }}</h4>
                            <small class="text-muted">تراکنش</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نرخ رشد -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info-subtle rounded-3 p-3">
                                <i class="fas fa-trending-up text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">نرخ رشد</h6>
                            <h4 class="mb-0 text-info">+{{ $data['growth_rate'] ?? 0 }}%</h4>
                            <small class="text-muted">نسبت به ماه قبل</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش جستجوی سریع کاربر -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-search me-2"></i>
                        جستجوی سریع کاربر
                    </h5>
                </div>
                <div class="card-body">
                    <div class="search-container position-relative">
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text"
                                   class="form-control border-start-0 ps-0"
                                   id="quickUserSearch"
                                   placeholder="جستجو بر اساس نام، نام خانوادگی، کد ملی، شماره موبایل یا ایمیل..."
                                   autocomplete="off">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <!-- نتایج جستجو -->
                        <div class="search-results position-absolute w-100 bg-white border rounded-3 shadow-lg mt-1 d-none"
                             id="searchResults"
                             style="z-index: 1050; max-height: 400px; overflow-y: auto;">
                            <div class="search-loading text-center py-3 d-none">
                                <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                <span class="text-muted">در حال جستجو...</span>
                            </div>
                            <div class="search-content"></div>
                            <div class="search-empty text-center py-4 text-muted d-none">
                                <i class="fas fa-user-slash fs-2 mb-2 d-block"></i>
                                <span>کاربری یافت نشد</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش دسترسی سریع -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-bolt me-2"></i>
                        دسترسی سریع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.users.create') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-primary-subtle">
                                    <i class="fas fa-user-plus text-primary"></i>
                                </div>
                                <span>افزودن کاربر</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.transaction.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-success-subtle">
                                    <i class="fas fa-exchange-alt text-success"></i>
                                </div>
                                <span>مدیریت تراکنش</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.activities.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-info-subtle">
                                    <i class="fas fa-chart-bar text-info"></i>
                                </div>
                                <span>گزارشات فعالیت</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.settings.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-warning-subtle">
                                    <i class="fas fa-cog text-warning"></i>
                                </div>
                                <span>تنظیمات</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.documents.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-secondary-subtle">
                                    <i class="fas fa-file-text text-secondary"></i>
                                </div>
                                <span>مدیریت اسناد</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.support.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-danger-subtle">
                                    <i class="fas fa-headset text-danger"></i>
                                </div>
                                <span>پشتیبانی</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش آمار و نمودارها -->
    <div class="row mb-4">
        <!-- نمودار تراکنش‌ها -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آمار تراکنش‌ها</h5>
                    <div class="card-actions">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary active">روزانه</button>
                            <button type="button" class="btn btn-sm btn-outline-primary">هفتگی</button>
                            <button type="button" class="btn btn-sm btn-outline-primary">ماهانه</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="transactions-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <!-- وضعیت سیستم -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">وضعیت سیستم</h5>
                </div>
                <div class="card-body">
                    <div class="system-status-item d-flex align-items-center mb-3">
                        <div class="status-icon bg-{{ $systemStatus['server']['status'] ? 'success' : 'danger' }}-subtle rounded-circle me-3">
                            <i class="fas fa-server text-{{ $systemStatus['server']['status'] ? 'success' : 'danger' }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">وضعیت سرور</h6>
                            <span class="text-{{ $systemStatus['server']['status'] ? 'success' : 'danger' }}">
                                {{ $systemStatus['server']['message'] }}
                            </span>
                        </div>
                        <span class="badge bg-{{ $systemStatus['server']['status'] ? 'success' : 'danger' }}">
                            {{ $systemStatus['server']['uptime'] }}%
                        </span>
                    </div>

                    <div class="system-status-item d-flex align-items-center mb-3">
                        <div class="status-icon bg-{{ $systemStatus['database']['status'] ? 'primary' : 'danger' }}-subtle rounded-circle me-3">
                            <i class="fas fa-database text-{{ $systemStatus['database']['status'] ? 'primary' : 'danger' }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">وضعیت دیتابیس</h6>
                            <span class="text-{{ $systemStatus['database']['status'] ? 'primary' : 'danger' }}">
                                {{ $systemStatus['database']['message'] }}
                            </span>
                        </div>
                        <span class="badge bg-{{ $systemStatus['database']['status'] ? 'primary' : 'danger' }}">
                            {{ $systemStatus['database']['uptime'] }}%
                        </span>
                    </div>

                    <div class="system-status-item d-flex align-items-center mb-3">
                        <div class="status-icon bg-{{ $systemStatus['memory']['status'] }}-subtle rounded-circle me-3">
                            <i class="fas fa-memory text-{{ $systemStatus['memory']['status'] }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">مصرف حافظه</h6>
                            <div class="progress mt-1" style="height: 6px;">
                                <div class="progress-bar bg-{{ $systemStatus['memory']['status'] }}"
                                     role="progressbar"
                                     style="width: {{ $systemStatus['memory']['usage'] }}%"
                                     aria-valuenow="{{ $systemStatus['memory']['usage'] }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                        <span class="badge bg-{{ $systemStatus['memory']['status'] }}">
                            {{ $systemStatus['memory']['usage'] }}%
                        </span>
                    </div>

                    <div class="system-status-item d-flex align-items-center">
                        <div class="status-icon bg-{{ $systemStatus['cpu']['status'] }}-subtle rounded-circle me-3">
                            <i class="fas fa-microchip text-{{ $systemStatus['cpu']['status'] }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">مصرف CPU</h6>
                            <div class="progress mt-1" style="height: 6px;">
                                <div class="progress-bar bg-{{ $systemStatus['cpu']['status'] }}"
                                     role="progressbar"
                                     style="width: {{ $systemStatus['cpu']['usage'] }}%"
                                     aria-valuenow="{{ $systemStatus['cpu']['usage'] }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                        <span class="badge bg-{{ $systemStatus['cpu']['status'] }}">
                            {{ $systemStatus['cpu']['usage'] }}%
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش آمار پیشرفته -->
    <div class="row mb-4">
        <!-- نمودار دونات درآمد -->
        <div class="col-xl-4 col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">توزیع درآمد</h5>
                </div>
                <div class="card-body">
                    <div id="revenue-donut-chart" style="height: 300px;"></div>
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">واریز مستقیم</span>
                            <span class="fw-bold">{{ number_format($data['direct_deposits'] ?? 0) }} تومان</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">کارمزد تراکنش</span>
                            <span class="fw-bold">{{ number_format($data['transaction_fees'] ?? 0) }} تومان</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">سایر درآمدها</span>
                            <span class="fw-bold">{{ number_format($data['other_revenue'] ?? 0) }} تومان</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آمار کاربران -->
        <div class="col-xl-4 col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">آمار کاربران</h5>
                </div>
                <div class="card-body">
                    <div class="user-stats">
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-success-subtle rounded me-3">
                                    <i class="fas fa-user-check text-success"></i>
                                </div>
                                <span>کاربران فعال</span>
                            </div>
                            <span class="fw-bold text-success">{{ number_format($data['active_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-warning-subtle rounded me-3">
                                    <i class="fas fa-user-clock text-warning"></i>
                                </div>
                                <span>در انتظار تایید</span>
                            </div>
                            <span class="fw-bold text-warning">{{ number_format($data['pending_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-danger-subtle rounded me-3">
                                    <i class="fas fa-user-times text-danger"></i>
                                </div>
                                <span>مسدود شده</span>
                            </div>
                            <span class="fw-bold text-danger">{{ number_format($data['blocked_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-info-subtle rounded me-3">
                                    <i class="fas fa-user-shield text-info"></i>
                                </div>
                                <span>VIP</span>
                            </div>
                            <span class="fw-bold text-info">{{ number_format($data['vip_users'] ?? 0) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخرین تراکنش‌ها -->
        <div class="col-xl-4 col-lg-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخرین تراکنش‌ها</h5>
                    <a href="{{ route('admin.transaction.index') }}" class="btn btn-sm btn-outline-primary">مشاهده همه</a>
                </div>
                <div class="card-body p-0">
                    <div class="transaction-list">
                        @forelse($latestTransactions ?? [] as $transaction)
                        <div class="transaction-item d-flex align-items-center p-3 border-bottom">
                            <div class="transaction-icon bg-{{ $transaction->status == 'completed' ? 'success' : ($transaction->status == 'pending' ? 'warning' : 'danger') }}-subtle rounded-circle me-3">
                                <i class="fas fa-{{ $transaction->type == 'deposit' ? 'arrow-down' : 'arrow-up' }} text-{{ $transaction->status == 'completed' ? 'success' : ($transaction->status == 'pending' ? 'warning' : 'danger') }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0">{{ $transaction->user->firstname ?? 'کاربر' }} {{ $transaction->user->lastname ?? '' }}</h6>
                                <small class="text-muted">{{ $transaction->created_at->diffForHumans() }}</small>
                            </div>
                            <div class="text-end">
                                <span class="fw-bold text-{{ $transaction->type == 'deposit' ? 'success' : 'danger' }}">
                                    {{ $transaction->type == 'deposit' ? '+' : '-' }}{{ number_format($transaction->amount) }}
                                </span>
                                <br>
                                <small class="text-muted">تومان</small>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-4">
                            <i class="fas fa-inbox text-muted fs-1 mb-3"></i>
                            <p class="text-muted">تراکنشی یافت نشد</p>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش آمار پیشرفته -->
    <div class="row mb-4">
        <!-- نمودار دونات درآمد -->
        <div class="col-xl-4 col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">توزیع درآمد</h5>
                </div>
                <div class="card-body">
                    <div id="revenue-donut-chart" style="height: 300px;"></div>
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">واریز مستقیم</span>
                            <span class="fw-bold">{{ number_format($data['direct_deposits'] ?? 0) }} تومان</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">کارمزد تراکنش</span>
                            <span class="fw-bold">{{ number_format($data['transaction_fees'] ?? 0) }} تومان</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">سایر درآمدها</span>
                            <span class="fw-bold">{{ number_format($data['other_revenue'] ?? 0) }} تومان</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آمار کاربران -->
        <div class="col-xl-4 col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">آمار کاربران</h5>
                </div>
                <div class="card-body">
                    <div class="user-stats">
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-success-subtle rounded me-3">
                                    <i class="fas fa-user-check text-success"></i>
                                </div>
                                <span>کاربران فعال</span>
                            </div>
                            <span class="fw-bold text-success">{{ number_format($data['active_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-warning-subtle rounded me-3">
                                    <i class="fas fa-user-clock text-warning"></i>
                                </div>
                                <span>در انتظار تایید</span>
                            </div>
                            <span class="fw-bold text-warning">{{ number_format($data['pending_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-danger-subtle rounded me-3">
                                    <i class="fas fa-user-times text-danger"></i>
                                </div>
                                <span>مسدود شده</span>
                            </div>
                            <span class="fw-bold text-danger">{{ number_format($data['blocked_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-info-subtle rounded me-3">
                                    <i class="fas fa-user-shield text-info"></i>
                                </div>
                                <span>VIP</span>
                            </div>
                            <span class="fw-bold text-info">{{ number_format($data['vip_users'] ?? 0) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخرین تراکنش‌ها -->
        <div class="col-xl-4 col-lg-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخرین تراکنش‌ها</h5>
                    <a href="{{ route('admin.transaction.index') }}" class="btn btn-sm btn-outline-primary">مشاهده همه</a>
                </div>
                <div class="card-body p-0">
                    <div class="transaction-list">
                        @forelse($latestTransactions ?? [] as $transaction)
                        <div class="transaction-item d-flex align-items-center p-3 border-bottom">
                            <div class="transaction-icon bg-{{ ($transaction->status ?? 'pending') == 'completed' ? 'success' : (($transaction->status ?? 'pending') == 'pending' ? 'warning' : 'danger') }}-subtle rounded-circle me-3">
                                <i class="fas fa-{{ ($transaction->type ?? 'deposit') == 'deposit' ? 'arrow-down' : 'arrow-up' }} text-{{ ($transaction->status ?? 'pending') == 'completed' ? 'success' : (($transaction->status ?? 'pending') == 'pending' ? 'warning' : 'danger') }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0">{{ $transaction->user->firstname ?? 'کاربر' }} {{ $transaction->user->lastname ?? '' }}</h6>
                                <small class="text-muted">{{ $transaction->created_at ? $transaction->created_at->diffForHumans() : 'نامشخص' }}</small>
                            </div>
                            <div class="text-end">
                                <span class="fw-bold text-{{ ($transaction->type ?? 'deposit') == 'deposit' ? 'success' : 'danger' }}">
                                    {{ ($transaction->type ?? 'deposit') == 'deposit' ? '+' : '-' }}{{ number_format($transaction->amount ?? 0) }}
                                </span>
                                <br>
                                <small class="text-muted">تومان</small>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-4">
                            <i class="fas fa-inbox text-muted fs-1 mb-3"></i>
                            <p class="text-muted">تراکنشی یافت نشد</p>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش فعالیت‌های اخیر و کاربران آنلاین -->
    <div class="row">
        <!-- هشدارهای اخیر -->
        <div class="col-xl-7 col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">هشدارهای اخیر</h5>
                    <a href="{{ route('admin.alerts.index') }}" class="btn btn-sm btn-primary">مشاهده همه</a>
                </div>
                <div class="card-body p-0">
                    <div class="activity-list">
                        @forelse($userAlerts as $alert)
                        <div class="activity-item d-flex p-3 border-bottom">
                            <div class="activity-icon bg-{{ $alert->type }}-subtle rounded-circle me-3">
                                @if($alert->type == 'success')
                                    <i class="fas fa-check-circle text-success"></i>
                                @elseif($alert->type == 'warning')
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                @elseif($alert->type == 'error')
                                    <i class="fas fa-times-circle text-danger"></i>
                                @elseif($alert->type == 'info')
                                    <i class="fas fa-info-circle text-info"></i>
                                @elseif($alert->type == 'primary')
                                    <i class="fas fa-bell text-primary"></i>
                                @endif
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    @if($alert->user)
                                        {{ $alert->user->firstname }} {{ $alert->user->lastname }}
                                    @else
                                        هشدار سیستم
                                    @endif
                                </h6>
                                <p class="mb-0 text-muted small">{{ $alert->message }}</p>
                                <span class="text-muted smaller">{{ $alert->time_ago }}</span>
                            </div>
                            <div class="ms-2">
                                @if(!$alert->read)
                                    <span class="badge bg-danger rounded-pill">جدید</span>
                                @endif
                            </div>
                        </div>
                        @empty
                        <div class="activity-item d-flex p-3 border-bottom">
                            <div class="activity-icon bg-info-subtle rounded-circle me-3">
                                <i class="fas fa-info-circle text-info"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">هشداری وجود ندارد</h6>
                                <p class="mb-0 text-muted small">در حال حاضر هیچ هشداری برای نمایش وجود ندارد.</p>
                            </div>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

        <!-- کاربران آنلاین -->
        <div class="col-xl-5 col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">کاربران آنلاین</h5>
                    <span class="badge bg-success">{{ $onlineUsers->count() }} آنلاین</span>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        @forelse($onlineUsers as $user)
                        <li class="list-group-item d-flex align-items-center px-3 py-3 user-item"
                            onclick="showUserDetails('{{ $user->id }}')"
                            style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="me-3 position-relative">
                                <img src="{{ $user->avatar ?? "https://ui-avatars.com/api/?name={$user->firstname}+{$user->lastname}&background=5a67d8&color=fff" }}"
                                     class="rounded-circle"
                                     width="40"
                                     height="40"
                                     alt="{{ $user->firstname }} {{ $user->lastname }}"
                                     style="transition: transform 0.3s ease;">
                                <span class="position-absolute bottom-0 end-0 transform translate-middle p-1 bg-success border border-light rounded-circle"
                                      style="box-shadow: 0 0 0 2px #fff;"></span>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0">{{ $user->firstname }} {{ $user->lastname }}</h6>
                                <small class="text-muted">{{ $user->email }}</small>
                            </div>
                            <div class="ms-2">
                                <i class="fas fa-chevron-left text-muted"></i>
                            </div>
                        </li>
                        @empty
                        <li class="list-group-item text-center py-3">
                            <p class="text-muted mb-0">کاربر آنلاینی وجود ندارد</p>
                        </li>
                        @endforelse
                    </ul>
                </div>

                <style>
                    .user-item:hover {
                        background: #f8fafc;
                        transform: translateX(-5px);
                    }
                    .user-item:hover img {
                        transform: scale(1.1);
                    }
                    .user-item:active {
                        transform: translateX(-2px);
                    }

                    /* استایل‌های جدید */
                    .quick-action-btn {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        text-decoration: none;
                        color: inherit;
                        padding: 1rem;
                        border-radius: 12px;
                        transition: all 0.3s ease;
                        background: #fff;
                        border: 1px solid #e2e8f0;
                    }

                    .quick-action-btn:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                        color: inherit;
                        text-decoration: none;
                    }

                    .quick-action-icon {
                        width: 50px;
                        height: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 12px;
                        margin-bottom: 0.5rem;
                        font-size: 1.2rem;
                    }

                    .quick-action-btn span {
                        font-size: 0.875rem;
                        font-weight: 500;
                        text-align: center;
                    }

                    .stat-icon-sm {
                        width: 35px;
                        height: 35px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .transaction-icon {
                        width: 40px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .bg-gradient-primary {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    }

                    .card {
                        transition: all 0.3s ease;
                    }

                    .card:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                    }

                    .stat-card {
                        padding: 1.5rem;
                        border-radius: 12px;
                        position: relative;
                        overflow: hidden;
                    }

                    .stat-card::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
                        pointer-events: none;
                    }

                    .stat-card.primary {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                    }

                    .stat-card.success {
                        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
                        color: white;
                    }

                    .stat-card.info {
                        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
                        color: white;
                    }

                    .stat-card.warning {
                        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
                        color: white;
                    }

                    .stat-icon {
                        font-size: 2rem;
                        opacity: 0.8;
                    }

                    .stat-content {
                        margin-left: 1rem;
                    }

                    .stat-value {
                        font-size: 1.75rem;
                        font-weight: 700;
                        line-height: 1;
                    }

                    .stat-label {
                        font-size: 0.875rem;
                        opacity: 0.9;
                        margin-top: 0.25rem;
                    }

                    .fade-in {
                        animation: fadeIn 0.5s ease-in;
                    }

                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(20px); }
                        to { opacity: 1; transform: translateY(0); }
                    }

                    /* استایل‌های جستجوی سریع */
                    .search-container {
                        max-width: 100%;
                    }

                    .search-results {
                        border: 1px solid #e2e8f0 !important;
                        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
                    }

                    .search-result-item {
                        padding: 12px 16px;
                        border-bottom: 1px solid #f1f5f9;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        display: flex;
                        align-items: center;
                    }

                    .search-result-item:hover {
                        background: #f8fafc;
                    }

                    .search-result-item:last-child {
                        border-bottom: none;
                    }

                    .search-result-avatar {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        margin-left: 12px;
                        background: #e2e8f0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: 600;
                        color: #64748b;
                    }

                    .search-result-info {
                        flex: 1;
                    }

                    .search-result-name {
                        font-weight: 600;
                        color: #1e293b;
                        margin-bottom: 2px;
                    }

                    .search-result-details {
                        font-size: 0.875rem;
                        color: #64748b;
                    }

                    .search-result-badge {
                        font-size: 0.75rem;
                        padding: 2px 8px;
                        border-radius: 12px;
                        margin-right: 8px;
                    }

                    .search-highlight {
                        background: #fef3c7;
                        padding: 1px 3px;
                        border-radius: 3px;
                        font-weight: 600;
                    }

                    #quickUserSearch:focus {
                        border-color: #667eea;
                        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
                    }

                    .input-group-text {
                        background: #f8fafc;
                        border-color: #e2e8f0;
                    }

                    .search-more {
                        padding: 12px 16px;
                        text-align: center;
                        background: #f8fafc;
                        border-top: 1px solid #e2e8f0;
                        color: #667eea;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    }

                    .search-more:hover {
                        background: #e2e8f0;
                        color: #5a67d8;
                    }
                </style>

                <script>
                    function showUserDetails(userId) {
                        Swal.fire({
                            title: 'جزئیات کاربر',
                            html: '<div class="text-center">در حال بارگذاری...</div>',
                            showConfirmButton: false,
                            showCloseButton: true,
                            customClass: {
                                popup: 'animated fadeInDown faster'
                            }
                        });

                        // اینجا می‌تونید با AJAX اطلاعات کاربر رو لود کنید
                        fetch(`/admin/users/${userId}/details`)
                            .then(response => response.json())
                            .then(data => {
                                Swal.update({
                                    html: `
                                        <div class="user-details p-3">
                                            <div class="text-center mb-4">
                                                <img src="${data.avatar}" class="rounded-circle mb-3" width="80" height="80">
                                                <h5 class="mb-1">${data.name}</h5>
                                                <p class="text-muted">${data.email}</p>
                                            </div>
                                            <div class="row text-start">
                                                <div class="col-6 mb-3">
                                                    <small class="text-muted">شماره تماس</small>
                                                    <p class="mb-0">${data.phone}</p>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <small class="text-muted">تاریخ عضویت</small>
                                                    <p class="mb-0">${data.created_at}</p>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">وضعیت</small>
                                                    <p class="mb-0">
                                                        <span class="badge bg-success">فعال</span>
                                                    </p>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">آخرین فعالیت</small>
                                                    <p class="mb-0">${data.last_activity}</p>
                                                </div>
                                            </div>
                                            <div class="mt-4">
                                                <a href="/admin/users/${userId}" class="btn btn-primary btn-sm">
                                                    مشاهده پروفایل کامل
                                                </a>
                                            </div>
                                        </div>
                                    `
                                });
                            });
                    }
                </script>
                <div class="card-footer text-center">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-sm btn-outline-primary">مشاهده همه کاربران</a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // نمودار تراکنش‌ها
        const transactionsChartOptions = {
            series: [{
                name: 'تراکنش‌های موفق',
                data: [31, 40, 28, 51, 42, 109, 100, 120, 80, 95, 110, 85]
            }, {
                name: 'تراکنش‌های ناموفق',
                data: [11, 32, 45, 32, 34, 52, 41, 30, 22, 13, 11, 5]
            }],
            chart: {
                height: 350,
                type: 'area',
                fontFamily: 'Vazirmatn, sans-serif',
                toolbar: {
                    show: false
                },
                zoom: {
                    enabled: false
                }
            },
            colors: ['#5a67d8', '#e53e3e'],
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.2,
                    stops: [0, 90, 100]
                }
            },
            xaxis: {
                categories: ['فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور', 'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند'],
                labels: {
                    style: {
                        colors: '#718096',
                        fontSize: '12px'
                    }
                },
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: '#718096',
                        fontSize: '12px'
                    },
                    formatter: function (value) {
                        return value.toLocaleString('fa-IR');
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function (value) {
                        return value.toLocaleString('fa-IR') + ' تراکنش';
                    }
                }
            },
            legend: {
                position: 'top',
                horizontalAlign: 'right',
                offsetY: -15,
                markers: {
                    width: 10,
                    height: 10,
                    radius: 12
                },
                itemMargin: {
                    horizontal: 10,
                    vertical: 0
                }
            },
            grid: {
                borderColor: '#f1f1f1',
                strokeDashArray: 4,
                xaxis: {
                    lines: {
                        show: true
                    }
                },
                yaxis: {
                    lines: {
                        show: true
                    }
                },
                padding: {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 10
                }
            }
        };

        const transactionsChart = new ApexCharts(document.querySelector('#transactions-chart'), transactionsChartOptions);
        transactionsChart.render();

        // نمودار دونات درآمد
        const revenueDonutOptions = {
            series: [65, 25, 10],
            chart: {
                type: 'donut',
                height: 300,
                fontFamily: 'Vazirmatn, sans-serif'
            },
            labels: ['واریز مستقیم', 'کارمزد تراکنش', 'سایر درآمدها'],
            colors: ['#48bb78', '#4299e1', '#ed8936'],
            legend: {
                position: 'bottom',
                horizontalAlign: 'center',
                fontSize: '14px',
                markers: {
                    width: 12,
                    height: 12,
                    radius: 12
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'مجموع',
                                fontSize: '16px',
                                fontWeight: 600,
                                color: '#373d3f',
                                formatter: function (w) {
                                    return w.globals.seriesTotals.reduce((a, b) => {
                                        return a + b
                                    }, 0) + '%'
                                }
                            }
                        }
                    }
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return val.toFixed(1) + "%"
                }
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + "%"
                    }
                }
            }
        };

        if (document.querySelector('#revenue-donut-chart')) {
            const revenueDonutChart = new ApexCharts(document.querySelector('#revenue-donut-chart'), revenueDonutOptions);
            revenueDonutChart.render();
        }

        // جستجوی سریع کاربر
        const quickSearchInput = document.getElementById('quickUserSearch');
        const searchResults = document.getElementById('searchResults');
        const searchContent = searchResults.querySelector('.search-content');
        const searchLoading = searchResults.querySelector('.search-loading');
        const searchEmpty = searchResults.querySelector('.search-empty');
        const clearSearchBtn = document.getElementById('clearSearch');

        let searchTimeout;
        let currentRequest;

        // تابع جستجو
        function performSearch(query) {
            if (query.length < 2) {
                hideSearchResults();
                return;
            }

            // لغو درخواست قبلی
            if (currentRequest) {
                currentRequest.abort();
            }

            showSearchLoading();

            // ایجاد درخواست جدید
            currentRequest = new AbortController();

            fetch(`/admin/users/quick-search?q=${encodeURIComponent(query)}`, {
                signal: currentRequest.signal,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                hideSearchLoading();
                displaySearchResults(data.users, query);
            })
            .catch(error => {
                if (error.name !== 'AbortError') {
                    hideSearchLoading();
                    showSearchEmpty();
                    console.error('خطا در جستجو:', error);
                }
            });
        }

        // نمایش نتایج جستجو
        function displaySearchResults(users, query) {
            if (users.length === 0) {
                showSearchEmpty();
                return;
            }

            const resultsHtml = users.map(user => {
                const avatar = user.avatar || `https://ui-avatars.com/api/?name=${user.firstname}+${user.lastname}&background=667eea&color=fff`;
                const fullName = `${user.firstname} ${user.lastname}`;
                const highlightedName = highlightText(fullName, query);
                const highlightedPhone = highlightText(user.phone || '', query);
                const highlightedEmail = highlightText(user.email || '', query);
                const highlightedNationalId = highlightText(user.national_id || '', query);

                return `
                    <div class="search-result-item" onclick="goToUser(${user.id})">
                        <div class="search-result-avatar">
                            <img src="${avatar}" alt="${fullName}" class="w-100 h-100 rounded-circle" style="object-fit: cover;">
                        </div>
                        <div class="search-result-info">
                            <div class="search-result-name">${highlightedName}</div>
                            <div class="search-result-details">
                                ${user.phone ? `📱 ${highlightedPhone}` : ''}
                                ${user.email ? `📧 ${highlightedEmail}` : ''}
                                ${user.national_id ? `🆔 ${highlightedNationalId}` : ''}
                            </div>
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <span class="search-result-badge bg-${getStatusColor(user.status)}-subtle text-${getStatusColor(user.status)}">
                                ${getStatusText(user.status)}
                            </span>
                            <small class="text-muted mt-1">ID: ${user.id}</small>
                        </div>
                    </div>
                `;
            }).join('');

            searchContent.innerHTML = resultsHtml;

            if (users.length >= 5) {
                searchContent.innerHTML += `
                    <div class="search-more" onclick="goToUsersPage('${query}')">
                        <i class="fas fa-plus-circle me-1"></i>
                        مشاهده همه نتایج
                    </div>
                `;
            }

            showSearchResults();
        }

        // هایلایت کردن متن
        function highlightText(text, query) {
            if (!text || !query) return text;
            const regex = new RegExp(`(${query})`, 'gi');
            return text.replace(regex, '<span class="search-highlight">$1</span>');
        }

        // تعیین رنگ وضعیت
        function getStatusColor(status) {
            switch(status) {
                case 'approved': return 'success';
                case 'pending': return 'warning';
                case 'rejected': return 'danger';
                case 'blocked': return 'dark';
                default: return 'secondary';
            }
        }

        // تعیین متن وضعیت
        function getStatusText(status) {
            switch(status) {
                case 'approved': return 'تایید شده';
                case 'pending': return 'در انتظار';
                case 'rejected': return 'رد شده';
                case 'blocked': return 'مسدود';
                default: return 'نامشخص';
            }
        }

        // نمایش نتایج
        function showSearchResults() {
            searchResults.classList.remove('d-none');
            searchEmpty.classList.add('d-none');
            searchLoading.classList.add('d-none');
        }

        // مخفی کردن نتایج
        function hideSearchResults() {
            searchResults.classList.add('d-none');
        }

        // نمایش لودینگ
        function showSearchLoading() {
            searchResults.classList.remove('d-none');
            searchLoading.classList.remove('d-none');
            searchEmpty.classList.add('d-none');
            searchContent.innerHTML = '';
        }

        // مخفی کردن لودینگ
        function hideSearchLoading() {
            searchLoading.classList.add('d-none');
        }

        // نمایش پیام خالی
        function showSearchEmpty() {
            searchResults.classList.remove('d-none');
            searchEmpty.classList.remove('d-none');
            searchLoading.classList.add('d-none');
            searchContent.innerHTML = '';
        }

        // تغییر نمودار با کلیک روی دکمه‌ها
        const chartButtons = document.querySelectorAll('.card-actions .btn');
        chartButtons.forEach(button => {
            button.addEventListener('click', function() {
                // حذف کلاس active از همه دکمه‌ها
                chartButtons.forEach(btn => btn.classList.remove('active'));
                // اضافه کردن کلاس active به دکمه کلیک شده
                this.classList.add('active');

                // تغییر داده‌های نمودار بر اساس دکمه انتخاب شده
                let newData;
                if (this.textContent === 'روزانه') {
                    newData = [
                        { name: 'تراکنش‌های موفق', data: [28, 45, 35, 50, 32, 55, 23] },
                        { name: 'تراکنش‌های ناموفق', data: [10, 15, 8, 12, 7, 16, 5] }
                    ];
                    transactionsChart.updateOptions({
                        xaxis: {
                            categories: ['شنبه', 'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنجشنبه', 'جمعه']
                        }
                    });
                } else if (this.textContent === 'هفتگی') {
                    newData = [
                        { name: 'تراکنش‌های موفق', data: [180, 220, 205, 190, 250] },
                        { name: 'تراکنش‌های ناموفق', data: [45, 55, 40, 35, 50] }
                    ];
                    transactionsChart.updateOptions({
                        xaxis: {
                            categories: ['هفته اول', 'هفته دوم', 'هفته سوم', 'هفته چهارم', 'هفته پنجم']
                        }
                    });
                } else if (this.textContent === 'ماهانه') {
                    newData = [
                        { name: 'تراکنش‌های موفق', data: [31, 40, 28, 51, 42, 109, 100, 120, 80, 95, 110, 85] },
                        { name: 'تراکنش‌های ناموفق', data: [11, 32, 45, 32, 34, 52, 41, 30, 22, 13, 11, 5] }
                    ];
                    transactionsChart.updateOptions({
                        xaxis: {
                            categories: ['فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور', 'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند']
                        }
                    });
                }

                transactionsChart.updateSeries(newData);
            });
        });

        // Event listeners برای جستجو
        quickSearchInput.addEventListener('input', function(e) {
            const query = e.target.value.trim();

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300);
        });

        // مخفی کردن نتایج با کلیک خارج از المان
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-container')) {
                hideSearchResults();
            }
        });

        // پاک کردن جستجو
        clearSearchBtn.addEventListener('click', function() {
            quickSearchInput.value = '';
            hideSearchResults();
            quickSearchInput.focus();
        });

        // کلیدهای کیبورد
        quickSearchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideSearchResults();
                this.blur();
            }
        });

        // توابع ناوبری
        window.goToUser = function(userId) {
            window.location.href = `/admin/users/${userId}`;
        };

        window.goToUsersPage = function(query) {
            window.location.href = `/admin/users?search=${encodeURIComponent(query)}`;
        };
    });
</script>
@endpush
